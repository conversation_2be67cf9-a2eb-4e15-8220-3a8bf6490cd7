import { Box } from 'zmp-ui';
import { useState } from 'react';
import clickSymbol from '../static/click-sympol.png';
import gameIcon from '../static/game-sticky.svg';
import messageIcon from '../static/message-text-sticky.svg';
import shareIcon from '../static/share-sticky.svg';

const StickyButtons = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleMenuItemClick = (action) => {
    console.log('Menu item clicked:', action);
    setIsMenuOpen(false); // Close menu after click
  };

  return (
    <Box className="sticky-buttons z-50 flex flex-col items-end space-y-3">
      {/* Dropdown Menu Items */}
      <Box className={`flex flex-col space-y-3 transition-all duration-300 ease-in-out ${
        isMenuOpen
          ? 'opacity-100 translate-y-0 pointer-events-auto'
          : 'opacity-0 translate-y-4 pointer-events-none'
      }`}>
        {/* Menu Item 3 - Game */}
        <button
          onClick={() => handleMenuItemClick('game')}
          className="w-14 h-14 bg-white hover:bg-gray-50 rounded-full flex items-center justify-center shadow-lg transition-all duration-200 transform hover:scale-105 border border-gray-200"
        >
          <img src={gameIcon} alt="Game" className="w-6 h-6 object-contain" />
        </button>

        {/* Menu Item 2 - Message */}
        <button
          onClick={() => handleMenuItemClick('message')}
          className="w-14 h-14 bg-white hover:bg-gray-50 rounded-full flex items-center justify-center shadow-lg transition-all duration-200 transform hover:scale-105 border border-gray-200"
        >
          <img src={messageIcon} alt="Message" className="w-6 h-6 object-contain" />
        </button>

        {/* Menu Item 1 - Share */}
        <button
          onClick={() => handleMenuItemClick('share')}
          className="w-14 h-14 bg-white hover:bg-gray-50 rounded-full flex items-center justify-center shadow-lg transition-all duration-200 transform hover:scale-105 border border-gray-200"
        >
          <img src={shareIcon} alt="Share" className="w-6 h-6 object-contain" />
        </button>
      </Box>

      {/* Menu Toggle Button */}
      <button
        onClick={toggleMenu}
        className={`w-14 h-14 bg-white hover:bg-gray-50 rounded-full flex items-center justify-center shadow-lg transition-all duration-300 transform hover:scale-105 border border-gray-200 ${
          isMenuOpen ? 'rotate-90' : 'rotate-0'
        }`}
      >
        {isMenuOpen ? (
          <svg className="w-7 h-7 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        ) : (
          <svg className="w-7 h-7" fill="none" stroke="#1531AD" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        )}
      </button>

      {/* Zalo Button */}
      <button className="w-14 h-14 bg-white hover:bg-gray-50 rounded-full flex items-center justify-center shadow-lg transition-all duration-200 transform hover:scale-105 border border-gray-200">
        <div className="flex flex-col items-center">
          <img src={clickSymbol} alt="Click Symbol" className="w-5 h-5 object-contain mb-0.5" />
          <div className="text-xs leading-tight font-bold" style={{ color: '#1531AD' }}>Về Zalo</div>
        </div>
      </button>
    </Box>
  );
};

export default StickyButtons;
