import { useState, useEffect } from 'react';
import { Box } from 'zmp-ui';
import QRCode from 'qrcode';

const DynamicQRCode = ({ 
  data, 
  size = 200, 
  className = "",
  onClick = null,
  alt = "QR Code"
}) => {
  const [qrCodeUrl, setQrCodeUrl] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  useEffect(() => {
    const generateQRCode = async () => {
      try {
        setLoading(true);
        setError(false);
        
        // Generate QR code with custom options
        const qrDataUrl = await QRCode.toDataURL(data, {
          width: size,
          margin: 2,
          color: {
            dark: '#000000',  // QR code color
            light: '#FFFFFF'  // Background color
          },
          errorCorrectionLevel: 'M'
        });
        
        setQrCodeUrl(qrDataUrl);
      } catch (err) {
        console.error('QR Code generation error:', err);
        setError(true);
      } finally {
        setLoading(false);
      }
    };

    if (data) {
      generateQRCode();
    }
  }, [data, size]);

  if (loading) {
    return (
      <Box className={`flex items-center justify-center ${className}`} style={{ width: size, height: size }}>
        <Box className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-blue"></Box>
      </Box>
    );
  }

  if (error) {
    return (
      <Box className={`flex items-center justify-center bg-gray-100 rounded-lg ${className}`} style={{ width: size, height: size }}>
        <span className="text-xs text-gray-500 text-center px-2">
          Không thể tạo QR code
        </span>
      </Box>
    );
  }

  return (
    <Box 
      className={`flex justify-center ${className} ${onClick ? 'cursor-pointer hover:opacity-80 transition-opacity' : ''}`}
      onClick={onClick}
    >
      <img 
        src={qrCodeUrl} 
        alt={alt}
        className="rounded-lg"
        style={{ width: size, height: size }}
      />
    </Box>
  );
};

export default DynamicQRCode;
